
import { HomePageLayout } from '@/components/SEOLayout';
import { ContentProvider } from '@/components/content/ContentProvider';
import { HeroSection, TestimonialsSection, ContactSection } from '@/components/content/ContentSection';
import { DynamicContent } from '@/components/content/DynamicContent';
import { DynamicServiceCards } from '@/components/DynamicServiceCards';
import { useLanguage } from '@/contexts/I18nContext';

// Legacy components (fallback)
import Hero from '@/components/Hero';

import TrustedPartners from '@/components/TrustedPartners';
import WhoWeAre from '@/components/WhoWeAre';
import KeyFeatures from '@/components/KeyFeatures';

import CTASection from '@/components/CTASection';
import TrainingSection from '@/components/TrainingSection';
import BusinessSolutionsSection from '@/components/BusinessSolutionsSection';

const IndexContent = () => {
  const { language } = useLanguage();

  return (
    <>
      {/* Dynamic Hero Section */}
      <HeroSection
        identifier="home-hero"
        language={language}
        fallback={<Hero />}
      />

      {/* Trusted Partners */}
      <DynamicContent
        identifier="trusted-partners"
        language={language}
        className="py-16 bg-gray-50"
        fallback={<TrustedPartners />}
      />

      {/* Who We Are Section */}
      <DynamicContent
        identifier="who-we-are"
        language={language}
        className="py-16"
        fallback={<WhoWeAre />}
      />

      {/* Dynamic Services Grid */}
      <DynamicServiceCards
        language={language}
        variant="homepage"
      />

      {/* Key Features */}
      <DynamicContent
        identifier="key-features"
        language={language}
        className="py-16"
        fallback={<KeyFeatures />}
      />

      {/* Business Solutions Section */}
      <BusinessSolutionsSection />

      {/* Training Programs Section */}
      <TrainingSection />

      {/* Dynamic Testimonials */}
      <TestimonialsSection
        language={language}
        className="py-16 bg-gray-50"
      />

      {/* CTA Section */}
      <DynamicContent
        identifier="cta-section"
        language={language}
        className="py-16"
        fallback={<CTASection />}
      />
    </>
  );
};

const Index = () => {
  const { language } = useLanguage();

  return (
    <ContentProvider key={language} defaultLanguage={language}>
      <HomePageLayout>
        <IndexContent />
      </HomePageLayout>
    </ContentProvider>
  );
};

export default Index;
