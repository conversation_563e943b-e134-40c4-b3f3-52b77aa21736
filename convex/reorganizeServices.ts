import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Query to check current service structure
export const getCurrentServices = query({
  args: {},
  handler: async (ctx) => {
    const services = await ctx.db
      .query("content")
      .filter((q) => q.eq(q.field("contentType.name"), "service_card"))
      .collect();

    return services.map(service => ({
      id: service._id,
      identifier: service.identifier,
      language: service.language,
      title: service.data.title,
      serviceType: service.data.serviceType || "main",
      parentService: service.data.parentService || null,
      order: service.data.order || 0
    }));
  },
});

// Mutation to reorganize services into hierarchical structure
export const reorganizeToHierarchicalServices = mutation({
  args: {},
  handler: async (ctx) => {
    const results = [];

    // Get service card content type
    const serviceCardType = await ctx.db
      .query("contentTypes")
      .filter((q) => q.eq(q.field("name"), "service_card"))
      .first();

    if (!serviceCardType) {
      throw new Error("Service card content type not found");
    }

    // Step 1: Update existing main services to have serviceType = "main"
    const mainServiceIdentifiers = [
      "service-training",
      "service-isp", 
      "service-procurement",
      "service-business-solutions"
    ];

    for (const identifier of mainServiceIdentifiers) {
      const services = await ctx.db
        .query("content")
        .withIndex("by_identifier", (q) => q.eq("identifier", identifier))
        .collect();

      for (const service of services) {
        await ctx.db.patch(service._id, {
          data: {
            ...service.data,
            serviceType: "main",
            parentService: null
          }
        });
        results.push(`Updated ${identifier} as main service`);
      }
    }

    // Step 2: Update Network Solutions to be sub-service under Business Solutions
    const networkServices = await ctx.db
      .query("content")
      .withIndex("by_identifier", (q) => q.eq("identifier", "service-network-solutions"))
      .collect();

    for (const service of networkServices) {
      await ctx.db.patch(service._id, {
        data: {
          ...service.data,
          serviceType: "sub",
          parentService: "service-business-solutions",
          order: 100 // Put sub-services after main services
        }
      });
      results.push(`Updated Network Solutions as sub-service under Business Solutions`);
    }

    // Step 3: Update Electronic Security to be sub-service under Business Solutions  
    const securityServices = await ctx.db
      .query("content")
      .withIndex("by_identifier", (q) => q.eq("identifier", "service-electronic-security"))
      .collect();

    for (const service of securityServices) {
      await ctx.db.patch(service._id, {
        data: {
          ...service.data,
          serviceType: "sub", 
          parentService: "service-business-solutions",
          order: 101 // Put after network solutions
        }
      });
      results.push(`Updated Electronic Security as sub-service under Business Solutions`);
    }

    return {
      message: "Successfully reorganized services into hierarchical structure",
      results,
      structure: {
        mainServices: [
          "Training",
          "ISP - Internet Services", 
          "Office Supplies & Equipment",
          "Business Solutions (with Network Solutions and Electronic Security as sub-services)"
        ]
      }
    };
  },
});

// Test query to see hierarchical structure
export const testHierarchicalStructure = query({
  args: {},
  handler: async (ctx) => {
    const language = "en";
    const includeSubServices = true;

    // Get all service cards in the specified language
    const allServices = await ctx.db
      .query("content")
      .filter((q) => 
        q.and(
          q.eq(q.field("language"), language),
          q.eq(q.field("status"), "published")
        )
      )
      .collect();

    const serviceCards = allServices.filter(item => 
      item.contentType?.name === "service_card"
    );

    // Separate main services and sub-services
    const mainServices = serviceCards
      .filter(service => (service.data.serviceType || "main") === "main")
      .sort((a, b) => (a.data.order || 0) - (b.data.order || 0));

    const subServices = serviceCards
      .filter(service => service.data.serviceType === "sub")
      .sort((a, b) => (a.data.order || 0) - (b.data.order || 0));

    // Build hierarchical structure
    const hierarchicalServices = mainServices.map(mainService => {
      const children = includeSubServices 
        ? subServices.filter(subService => 
            subService.data.parentService === mainService.identifier
          )
        : [];

      return {
        ...mainService,
        children
      };
    });

    return hierarchicalServices;
  },
});

// Query to get hierarchical service structure
export const getHierarchicalServices = query({
  args: {
    language: v.optional(v.string()),
    includeSubServices: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    const language = args.language || "en";
    const includeSubServices = args.includeSubServices ?? true;

    // Get all service cards in the specified language
    const allServices = await ctx.db
      .query("content")
      .filter((q) =>
        q.and(
          q.eq(q.field("language"), language),
          q.eq(q.field("status"), "published")
        )
      )
      .collect();

    const serviceCards = allServices.filter(item =>
      item.contentType?.name === "service_card"
    );

    // Separate main services and sub-services
    const mainServices = serviceCards
      .filter(service => (service.data.serviceType || "main") === "main")
      .sort((a, b) => (a.data.order || 0) - (b.data.order || 0));

    const subServices = serviceCards
      .filter(service => service.data.serviceType === "sub")
      .sort((a, b) => (a.data.order || 0) - (b.data.order || 0));

    // Build hierarchical structure
    const hierarchicalServices = mainServices.map(mainService => {
      const children = includeSubServices
        ? subServices.filter(subService =>
            subService.data.parentService === mainService.identifier
          )
        : [];

      return {
        ...mainService,
        children
      };
    });

    return hierarchicalServices;
  },
});
