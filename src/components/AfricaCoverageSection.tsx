import { useLanguage } from '@/contexts/I18nContext';
import { Globe, MapPin, Users, Building2 } from 'lucide-react';

const AfricaCoverageSection = () => {
  const { language } = useLanguage();

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Content Side */}
          <div className="space-y-8">
            {/* Main Title */}
            <div>
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <Globe className="w-6 h-6 text-blue-600" />
                </div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                  {language === 'en' ? 'Our Coverage' : 'Nuestra Cobertura'}
                </h2>
              </div>
              <p className="text-xl text-gray-600 leading-relaxed">
                {language === 'en'
                  ? 'Serving Equatorial Guinea & Central Africa with comprehensive technology solutions and reliable connectivity.'
                  : 'Sirviendo a Guinea Ecuatorial y África Central con soluciones tecnológicas integrales y conectividad confiable.'
                }
              </p>
            </div>

            {/* Coverage Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-white rounded-xl shadow-sm border border-blue-100">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <MapPin className="w-5 h-5 text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">3+</div>
                <div className="text-sm text-gray-600">
                  {language === 'en' ? 'Major Cities' : 'Ciudades Principales'}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {language === 'en' ? 'Bata, Malabo, Mongomo' : 'Bata, Malabo, Mongomo'}
                </div>
              </div>

              <div className="text-center p-4 bg-white rounded-xl shadow-sm border border-blue-100">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Users className="w-5 h-5 text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">500+</div>
                <div className="text-sm text-gray-600">
                  {language === 'en' ? 'Clients Served' : 'Clientes Atendidos'}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {language === 'en' ? 'Businesses & Organizations' : 'Empresas y Organizaciones'}
                </div>
              </div>

              <div className="text-center p-4 bg-white rounded-xl shadow-sm border border-blue-100">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Building2 className="w-5 h-5 text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">15+</div>
                <div className="text-sm text-gray-600">
                  {language === 'en' ? 'Years Experience' : 'Años de Experiencia'}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {language === 'en' ? 'In Central Africa' : 'En África Central'}
                </div>
              </div>
            </div>

            {/* Key Locations */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-blue-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {language === 'en' ? 'Strategic Locations' : 'Ubicaciones Estratégicas'}
              </h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  <div>
                    <span className="font-medium text-gray-900">Malabo</span>
                    <span className="text-gray-600 ml-2">
                      {language === 'en' ? '- Capital & Business Hub' : '- Capital y Centro de Negocios'}
                    </span>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  <div>
                    <span className="font-medium text-gray-900">Bata</span>
                    <span className="text-gray-600 ml-2">
                      {language === 'en' ? '- Economic Center' : '- Centro Económico'}
                    </span>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  <div>
                    <span className="font-medium text-gray-900">Mongomo</span>
                    <span className="text-gray-600 ml-2">
                      {language === 'en' ? '- Regional Operations' : '- Operaciones Regionales'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Africa Map Side */}
          <div className="relative order-first lg:order-last">
            {/* Main Map Container */}
            <div className="relative bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl p-6 lg:p-8 shadow-xl">
              <div className="relative">
                <img
                  src="/img/africa map.webp"
                  alt="Africa Coverage Map - OfficeTech Equatorial Guinea"
                  className="w-full h-auto max-w-sm lg:max-w-md mx-auto drop-shadow-lg"
                />
                
                {/* Location Markers */}
                <div className="absolute top-1/3 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="relative">
                    <div className="w-3 h-3 lg:w-4 lg:h-4 bg-red-500 rounded-full border-2 border-white shadow-lg animate-pulse"></div>
                    <div className="absolute -top-6 lg:-top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                      Equatorial Guinea
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Decorative Elements */}
              <div className="absolute top-4 right-4 w-8 h-8 bg-blue-300 rounded-full opacity-50"></div>
              <div className="absolute bottom-6 left-6 w-6 h-6 bg-blue-400 rounded-full opacity-30"></div>
              <div className="absolute top-1/2 right-8 w-3 h-3 bg-blue-500 rounded-full opacity-40"></div>
            </div>

            {/* Coverage Indicator */}
            <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 bg-white rounded-full px-6 py-3 shadow-lg border border-blue-100">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-700">
                  {language === 'en' ? 'Active Coverage' : 'Cobertura Activa'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AfricaCoverageSection;
