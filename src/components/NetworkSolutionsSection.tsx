import { <PERSON> } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Network,
  Globe,
  Building2,
  Wifi,
  ArrowRight,
  Shield,
  Zap,
  Users,
  Camera,
  Lock,
  Monitor,
  Cloud,
  Database,
  Smartphone
} from 'lucide-react';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useLanguage } from '@/contexts/I18nContext';
import { DynamicContent } from '@/components/content/DynamicContent';

const BusinessSolutionsSection = () => {
  const { language } = useLanguage();

  // Get all business solution related content in current language
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for network solution cards and sort by order
  const networkSolutions = allContent?.filter(item =>
    item.contentType?.name === "network_solution" && item.data.slug
  ).sort((a, b) => (a.data.order || 0) - (b.data.order || 0)) || [];

  // Get electronic security solutions (if they exist as content)
  const securitySolutions = allContent?.filter(item =>
    item.contentType?.name === "security_solution" && item.data.slug
  ).sort((a, b) => (a.data.order || 0) - (b.data.order || 0)) || [];

  // Fallback data for network solutions
  const fallbackNetworkSolutions = [
    {
      id: 'national-connectivity',
      title: language === 'en' ? 'National Connectivity' : 'Conectividad Nacional',
      description: language === 'en' ? 'Comprehensive network solutions across the entire country with coverage in Bata, Malabo & Mongomo.' : 'Soluciones de red integrales en todo el país con cobertura en Bata, Malabo y Mongomo.',
      icon: Globe,
      category: 'network',
      features: language === 'en' ? [
        'Private Networks',
        'High-Speed Internet',
        'Security Solutions',
        '24/7 Technical Support'
      ] : [
        'Redes Privadas',
        'Internet de Alta Velocidad',
        'Soluciones de Seguridad',
        'Soporte Técnico 24/7'
      ],
      link: '/network-solutions/national-connectivity'
    },
    {
      id: 'international-connectivity',
      title: language === 'en' ? 'International Connectivity' : 'Conectividad Internacional',
      description: language === 'en' ? 'Advanced solutions like MPLS, SD-WAN, and VPL to ensure your business stays connected globally.' : 'Soluciones avanzadas como MPLS, SD-WAN y VPL para asegurar que tu negocio se mantenga conectado globalmente.',
      icon: Network,
      category: 'network',
      features: language === 'en' ? [
        'MPLS (Multi-Protocol Label Switching)',
        'SD-WAN (Software-Defined WAN)',
        'VPL (Layer 2 VPN)',
        'Uninterrupted Connectivity'
      ] : [
        'MPLS (Conmutación de Etiquetas Multi-Protocolo)',
        'SD-WAN (WAN Definida por Software)',
        'VPL (VPN de Capa 2)',
        'Conectividad Ininterrumpida'
      ],
      link: '/network-solutions/international-connectivity'
    },
    {
      id: 'branch-office-connectivity',
      title: language === 'en' ? 'Branch Office Connectivity' : 'Conectividad de Sucursales',
      description: language === 'en' ? 'Keep your enterprise connected, collaborative, and competitive across all branch locations.' : 'Mantén tu empresa conectada, colaborativa y competitiva en todas las ubicaciones de sucursales.',
      icon: Building2,
      category: 'network',
      features: language === 'en' ? [
        'High-Speed Connectivity',
        'Centralized Management',
        'Enhanced Security',
        'Cost Efficiency & Scalability'
      ] : [
        'Conectividad de Alta Velocidad',
        'Gestión Centralizada',
        'Seguridad Mejorada',
        'Eficiencia de Costos y Escalabilidad'
      ],
      link: '/network-solutions/branch-office-connectivity'
    },
    {
      id: 'managed-enterprise-internet',
      title: language === 'en' ? 'Managed Enterprise Internet' : 'Internet Empresarial Gestionado',
      description: language === 'en' ? 'Comprehensive end-to-end internet infrastructure solutions with proactive monitoring and expert support.' : 'Soluciones integrales de infraestructura de internet de extremo a extremo con monitoreo proactivo y soporte experto.',
      icon: Wifi,
      category: 'network',
      features: language === 'en' ? [
        'Reliable High-Speed Connectivity',
        'Proactive Monitoring & Maintenance',
        'Enterprise-Grade Security',
        'Expert Support 24/7'
      ] : [
        'Conectividad Confiable de Alta Velocidad',
        'Monitoreo y Mantenimiento Proactivo',
        'Seguridad de Grado Empresarial',
        'Soporte Experto 24/7'
      ],
      link: '/network-solutions/managed-enterprise-internet'
    }
  ];

  // Fallback data for electronic security solutions
  const fallbackSecuritySolutions = [
    {
      id: 'surveillance-systems',
      title: language === 'en' ? 'Surveillance Systems' : 'Sistemas de Vigilancia',
      description: language === 'en' ? 'Advanced CCTV and monitoring solutions to protect your business premises 24/7.' : 'Sistemas avanzados de CCTV y monitoreo para proteger las instalaciones de tu negocio 24/7.',
      icon: Camera,
      category: 'security',
      features: language === 'en' ? [
        'HD/4K Camera Systems',
        'Remote Monitoring',
        'Motion Detection',
        'Cloud Storage Options'
      ] : [
        'Sistemas de Cámaras HD/4K',
        'Monitoreo Remoto',
        'Detección de Movimiento',
        'Opciones de Almacenamiento en la Nube'
      ],
      link: '/services/electronic-security'
    },
    {
      id: 'access-control',
      title: language === 'en' ? 'Access Control Systems' : 'Sistemas de Control de Acceso',
      description: language === 'en' ? 'Secure your facilities with advanced access control and authentication systems.' : 'Asegura tus instalaciones con sistemas avanzados de control de acceso y autenticación.',
      icon: Lock,
      category: 'security',
      features: language === 'en' ? [
        'Biometric Authentication',
        'Card-Based Access',
        'Time & Attendance Tracking',
        'Integration with Security Systems'
      ] : [
        'Autenticación Biométrica',
        'Acceso Basado en Tarjetas',
        'Seguimiento de Tiempo y Asistencia',
        'Integración con Sistemas de Seguridad'
      ],
      link: '/services/electronic-security'
    },
    {
      id: 'alarm-systems',
      title: language === 'en' ? 'Alarm & Monitoring' : 'Alarmas y Monitoreo',
      description: language === 'en' ? 'Comprehensive alarm systems with 24/7 monitoring and rapid response capabilities.' : 'Sistemas de alarma integrales con monitoreo 24/7 y capacidades de respuesta rápida.',
      icon: Monitor,
      category: 'security',
      features: language === 'en' ? [
        'Intrusion Detection',
        '24/7 Monitoring Center',
        'Mobile Alerts',
        'Emergency Response'
      ] : [
        'Detección de Intrusión',
        'Centro de Monitoreo 24/7',
        'Alertas Móviles',
        'Respuesta de Emergencia'
      ],
      link: '/services/electronic-security'
    }
  ];

  // Additional business solutions
  const fallbackBusinessSolutions = [
    {
      id: 'cloud-solutions',
      title: language === 'en' ? 'Cloud Solutions' : 'Soluciones en la Nube',
      description: language === 'en' ? 'Migrate and manage your business applications and data in secure cloud environments.' : 'Migra y gestiona las aplicaciones y datos de tu negocio en entornos de nube seguros.',
      icon: Cloud,
      category: 'business',
      features: language === 'en' ? [
        'Cloud Migration Services',
        'Data Backup & Recovery',
        'Scalable Infrastructure',
        'Cost Optimization'
      ] : [
        'Servicios de Migración a la Nube',
        'Respaldo y Recuperación de Datos',
        'Infraestructura Escalable',
        'Optimización de Costos'
      ],
      link: '/services/business-solutions'
    },
    {
      id: 'data-management',
      title: language === 'en' ? 'Data Management' : 'Gestión de Datos',
      description: language === 'en' ? 'Comprehensive data storage, backup, and analytics solutions for your business intelligence needs.' : 'Soluciones integrales de almacenamiento, respaldo y análisis de datos para las necesidades de inteligencia empresarial.',
      icon: Database,
      category: 'business',
      features: language === 'en' ? [
        'Database Management',
        'Business Intelligence',
        'Data Analytics',
        'Compliance & Security'
      ] : [
        'Gestión de Bases de Datos',
        'Inteligencia Empresarial',
        'Análisis de Datos',
        'Cumplimiento y Seguridad'
      ],
      link: '/services/business-solutions'
    },
    {
      id: 'mobile-solutions',
      title: language === 'en' ? 'Mobile Solutions' : 'Soluciones Móviles',
      description: language === 'en' ? 'Custom mobile applications and enterprise mobility solutions to keep your business connected.' : 'Aplicaciones móviles personalizadas y soluciones de movilidad empresarial para mantener tu negocio conectado.',
      icon: Smartphone,
      category: 'business',
      features: language === 'en' ? [
        'Custom App Development',
        'Enterprise Mobility',
        'Mobile Device Management',
        'Cross-Platform Solutions'
      ] : [
        'Desarrollo de Aplicaciones Personalizadas',
        'Movilidad Empresarial',
        'Gestión de Dispositivos Móviles',
        'Soluciones Multiplataforma'
      ],
      link: '/services/business-solutions'
    }
  ];

  // Combine all solutions - use dynamic content if available, otherwise fallback
  const allNetworkSolutions = networkSolutions.length > 0 ? networkSolutions : fallbackNetworkSolutions;
  const allSecuritySolutions = securitySolutions.length > 0 ? securitySolutions : fallbackSecuritySolutions;

  // Combine all business solutions
  const displaySolutions = [
    ...allNetworkSolutions,
    ...allSecuritySolutions,
    ...fallbackBusinessSolutions
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-4">
              <Building2 className="w-8 h-8 text-blue-600" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold">
              {language === 'en' ? 'Business' : 'Soluciones'} <span className="text-blue-300">{language === 'en' ? 'Solutions' : 'Empresariales'}</span>
            </h2>
          </div>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
            {language === 'en'
              ? 'Comprehensive technology solutions to transform your business. From network infrastructure and security systems to cloud services and data management, we provide integrated solutions that drive growth and efficiency.'
              : 'Soluciones tecnológicas integrales para transformar tu negocio. Desde infraestructura de red y sistemas de seguridad hasta servicios en la nube y gestión de datos, proporcionamos soluciones integradas que impulsan el crecimiento y la eficiencia.'
            }
          </p>
        </div>

        {/* Network Solutions Subsection */}
        <div className="mb-16">
          <div className="flex items-center mb-8">
            <Network className="w-6 h-6 text-blue-300 mr-3" />
            <h3 className="text-2xl font-bold text-white">
              {language === 'en' ? 'Network Solutions' : 'Soluciones de Red'}
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {allNetworkSolutions.map((solution) => {
              const IconComponent = solution.icon || Network;
              const title = solution.data?.title || solution.title;
              const description = solution.data?.description || solution.description;
              const features = solution.data?.features || solution.features || [];
              const link = solution.data?.slug ? `/network-solutions/${solution.data.slug}` : solution.link;

              return (
                <Card
                  key={solution.id || solution._id}
                  className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 bg-white/10 backdrop-blur-sm border-white/20 text-white"
                >
                  <CardContent className="p-6">
                    <div className="flex items-start mb-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-white transition-colors duration-300">
                        <IconComponent className="w-6 h-6 text-blue-600 group-hover:text-blue-700 transition-colors duration-300" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-bold mb-2">{title}</h4>
                      </div>
                    </div>
                    <p className="text-blue-100 mb-4 leading-relaxed text-sm">{description}</p>
                    <ul className="space-y-1 mb-4">
                      {features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="text-xs text-blue-200 flex items-center">
                          <div className="w-1 h-1 bg-blue-300 rounded-full mr-2"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Link to={link}>
                      <Button
                        size="sm"
                        variant="outline"
                        className="w-full bg-transparent border-white/30 text-white hover:bg-white hover:text-blue-900 transition-all duration-300"
                      >
                        {language === 'en' ? 'Learn More' : 'Saber Más'}
                        <ArrowRight className="ml-2 h-3 w-3" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Electronic Security Subsection */}
        <div className="mb-16">
          <div className="flex items-center mb-8">
            <Shield className="w-6 h-6 text-blue-300 mr-3" />
            <h3 className="text-2xl font-bold text-white">
              {language === 'en' ? 'Electronic Security' : 'Seguridad Electrónica'}
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {allSecuritySolutions.map((solution) => {
              const IconComponent = solution.icon || Camera;
              const title = solution.data?.title || solution.title;
              const description = solution.data?.description || solution.description;
              const features = solution.data?.features || solution.features || [];
              const link = solution.data?.slug ? `/services/${solution.data.slug}` : solution.link;

              return (
                <Card
                  key={solution.id || solution._id}
                  className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 bg-white/10 backdrop-blur-sm border-white/20 text-white"
                >
                  <CardContent className="p-6">
                    <div className="flex items-start mb-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-white transition-colors duration-300">
                        <IconComponent className="w-6 h-6 text-blue-600 group-hover:text-blue-700 transition-colors duration-300" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-bold mb-2">{title}</h4>
                      </div>
                    </div>
                    <p className="text-blue-100 mb-4 leading-relaxed text-sm">{description}</p>
                    <ul className="space-y-1 mb-4">
                      {features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="text-xs text-blue-200 flex items-center">
                          <div className="w-1 h-1 bg-blue-300 rounded-full mr-2"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Link to={link}>
                      <Button
                        size="sm"
                        variant="outline"
                        className="w-full bg-transparent border-white/30 text-white hover:bg-white hover:text-blue-900 transition-all duration-300"
                      >
                        {language === 'en' ? 'Learn More' : 'Saber Más'}
                        <ArrowRight className="ml-2 h-3 w-3" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Additional Business Solutions Subsection */}
        <div className="mb-16">
          <div className="flex items-center mb-8">
            <Cloud className="w-6 h-6 text-blue-300 mr-3" />
            <h3 className="text-2xl font-bold text-white">
              {language === 'en' ? 'Technology Solutions' : 'Soluciones Tecnológicas'}
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {fallbackBusinessSolutions.map((solution) => {
              const IconComponent = solution.icon || Cloud;
              const title = solution.title;
              const description = solution.description;
              const features = solution.features || [];
              const link = solution.link;

              return (
                <Card
                  key={solution.id}
                  className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 bg-white/10 backdrop-blur-sm border-white/20 text-white"
                >
                  <CardContent className="p-6">
                    <div className="flex items-start mb-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-white transition-colors duration-300">
                        <IconComponent className="w-6 h-6 text-blue-600 group-hover:text-blue-700 transition-colors duration-300" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-bold mb-2">{title}</h4>
                      </div>
                    </div>
                    <p className="text-blue-100 mb-4 leading-relaxed text-sm">{description}</p>
                    <ul className="space-y-1 mb-4">
                      {features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="text-xs text-blue-200 flex items-center">
                          <div className="w-1 h-1 bg-blue-300 rounded-full mr-2"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Link to={link}>
                      <Button
                        size="sm"
                        variant="outline"
                        className="w-full bg-transparent border-white/30 text-white hover:bg-white hover:text-blue-900 transition-all duration-300"
                      >
                        {language === 'en' ? 'Learn More' : 'Saber Más'}
                        <ArrowRight className="ml-2 h-3 w-3" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Key Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">
              {language === 'en' ? 'Comprehensive Security' : 'Seguridad Integral'}
            </h3>
            <p className="text-blue-200">
              {language === 'en'
                ? 'End-to-end security solutions from network protection to physical surveillance systems.'
                : 'Soluciones de seguridad de extremo a extremo desde protección de red hasta sistemas de vigilancia física.'
              }
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Zap className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">
              {language === 'en' ? 'Integrated Solutions' : 'Soluciones Integradas'}
            </h3>
            <p className="text-blue-200">
              {language === 'en'
                ? 'Seamlessly integrated technology solutions that work together to optimize your business operations.'
                : 'Soluciones tecnológicas perfectamente integradas que trabajan juntas para optimizar las operaciones de tu negocio.'
              }
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">
              {language === 'en' ? '24/7 Expert Support' : 'Soporte Experto 24/7'}
            </h3>
            <p className="text-blue-200">
              {language === 'en'
                ? 'Dedicated team of specialists available around the clock for all your business technology needs.'
                : 'Equipo dedicado de especialistas disponible las 24 horas para todas tus necesidades de tecnología empresarial.'
              }
            </p>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
          <h3 className="text-2xl font-bold mb-4">
            {language === 'en'
              ? 'Ready to Transform Your Business Technology?'
              : '¿Listo para Transformar la Tecnología de tu Negocio?'
            }
          </h3>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            {language === 'en'
              ? 'From network infrastructure and security systems to cloud solutions and data management, our comprehensive business solutions are designed to drive growth, enhance security, and optimize operations.'
              : 'Desde infraestructura de red y sistemas de seguridad hasta soluciones en la nube y gestión de datos, nuestras soluciones empresariales integrales están diseñadas para impulsar el crecimiento, mejorar la seguridad y optimizar las operaciones.'
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/services/business-solutions">
              <Button size="lg" className="bg-white text-blue-900 hover:bg-blue-50 px-8">
                {language === 'en' ? 'Explore All Solutions' : 'Explorar Todas las Soluciones'}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/contact">
              <Button size="lg" variant="outline" className="border-white/30 text-white hover:bg-white hover:text-blue-900 px-8">
                <Building2 className="mr-2 h-5 w-5" />
                {language === 'en' ? 'Get Business Assessment' : 'Obtener Evaluación Empresarial'}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BusinessSolutionsSection;
